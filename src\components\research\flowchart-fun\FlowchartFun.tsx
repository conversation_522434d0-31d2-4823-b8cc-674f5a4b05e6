/**
 * Flowchart Fun - Main Component
 * Text-to-flowchart visualization tool with tab-based interface
 */

import React, { useState, useEffect } from 'react';
import * as Tabs from '@radix-ui/react-tabs';
import { Allotment } from 'allotment';
import 'allotment/dist/style.css';
import { FlowchartFunProps } from './types';
import { useFlowchartFunStore } from './stores/flowchart-fun.store';
import {
  FlowchartTextEditor,
  FlowchartGraphViewer,
  FlowchartExportDialog,
  AiToolbar
} from './components';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import {
  Download,
  Settings,
  Sun,
  Moon,
  Layout,
  FileText,
  Share2,
  HelpCircle,
  Sparkles,
  GitBranch,
  Zap,
  Eye,
  EyeOff,
  BookOpen,
  Upload,
  Database,
  Lightbulb,
  File,
  Palette
} from 'lucide-react';
import { toast } from 'sonner';
import { SAMPLE_FLOWCHARTS } from './constants/ai-examples';
import { DEFAULT_TEMPLATES } from './constants';

export function FlowchartFun({
  className = '',
  initialText,
  onTextChange,
  onGraphChange,
  readOnly = false
}: FlowchartFunProps) {
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [activeTab, setActiveTab] = useState('Document');
  
  const {
    text,
    parsedGraph,
    parseErrors,
    isValidSyntax,
    theme,
    layoutAlgorithm,
    selectedNode,
    selectedEdge,
    canUndo,
    canRedo,
    setText,
    setTheme,
    setLayoutAlgorithm,
    undo,
    redo,
    clearSelection
  } = useFlowchartFunStore();

  // Initialize with provided text
  useEffect(() => {
    if (initialText && initialText !== text) {
      setText(initialText);
    }
  }, [initialText, setText, text]);

  // Notify parent of text changes
  useEffect(() => {
    onTextChange?.(text);
  }, [text, onTextChange]);

  // Notify parent of graph changes
  useEffect(() => {
    if (parsedGraph) {
      onGraphChange?.(parsedGraph);
    }
  }, [parsedGraph, onGraphChange]);

  const handleExport = () => {
    if (!parsedGraph) {
      toast.error('No flowchart to export');
      return;
    }
    setIsExportDialogOpen(true);
  };

  const handleShare = async () => {
    if (!text) {
      toast.error('No content to share');
      return;
    }

    try {
      await navigator.clipboard.writeText(text);
      toast.success('Flowchart text copied to clipboard');
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const handleLayoutChange = (layout: string) => {
    setLayoutAlgorithm(layout as any);
    toast.success(`Layout changed to ${layout}`);
  };

  const getStatusInfo = () => {
    if (!parsedGraph) return { nodes: 0, edges: 0, status: 'empty' };

    return {
      nodes: parsedGraph.nodes.length,
      edges: parsedGraph.edges.length,
      status: isValidSyntax ? 'valid' : 'error'
    };
  };

  const loadExample = (exampleKey: keyof typeof SAMPLE_FLOWCHARTS) => {
    setText(SAMPLE_FLOWCHARTS[exampleKey]);
    setActiveTab('Document');
    toast.success('Example loaded');
  };

  const loadTemplate = (template: typeof DEFAULT_TEMPLATES[0]) => {
    setText(template.text);
    setActiveTab('Document');
    toast.success('Template loaded');
  };

  const handleFileImport = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.txt,.md,.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const content = e.target?.result as string;
          setText(content);
          setActiveTab('Document');
          toast.success('File imported successfully');
        };
        reader.readAsText(file);
      }
    };
    input.click();
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const statusInfo = getStatusInfo();

  return (
    <TooltipProvider>
      <div className={`h-full flex flex-col ${theme === 'dark' ? 'dark bg-gray-900 text-white' : 'bg-white'} ${className}`}>
        {/* Tab Navigation */}
        <Tabs.Root value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <Tabs.List className="flex bg-white dark:bg-gray-900">
              <Tabs.Trigger
                value="Document"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <File className="h-4 w-4 mr-2" />
                Document
              </Tabs.Trigger>

              <Tabs.Trigger
                value="Theme"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <Palette className="h-4 w-4 mr-2" />
                Theme
              </Tabs.Trigger>

              <Tabs.Trigger
                value="Examples"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <Lightbulb className="h-4 w-4 mr-2" />
                Examples
              </Tabs.Trigger>

              <Tabs.Trigger
                value="Learn Syntax"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Learn Syntax
              </Tabs.Trigger>

              <Tabs.Trigger
                value="Load File"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <Upload className="h-4 w-4 mr-2" />
                Load File
              </Tabs.Trigger>

              <Tabs.Trigger
                value="Import Data"
                className="px-6 py-3 text-lg font-bold text-gray-700 dark:text-gray-300 opacity-50 aria-selected:opacity-100 aria-selected:shadow-xl bg-white dark:bg-gray-900 border-none outline-none"
              >
                <Database className="h-4 w-4 mr-2" />
                Import Data
              </Tabs.Trigger>
            </Tabs.List>
          </div>

          <div className="flex items-center space-x-2">
            {/* Undo/Redo */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={undo}
                  disabled={!canUndo || readOnly}
                >
                  <span className="sr-only">Undo</span>
                  ↶
                </Button>
              </TooltipTrigger>
              <TooltipContent>Undo</TooltipContent>
            </Tooltip>

            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={redo}
                  disabled={!canRedo || readOnly}
                >
                  <span className="sr-only">Redo</span>
                  ↷
                </Button>
              </TooltipTrigger>
              <TooltipContent>Redo</TooltipContent>
            </Tooltip>

            <Separator orientation="vertical" className="h-6" />

            {/* Preview Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {showPreview ? 'Hide Preview' : 'Show Preview'}
              </TooltipContent>
            </Tooltip>

            {/* Layout Selector */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <Layout className="h-4 w-4 mr-2" />
                  Layout
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Layout Algorithm</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleLayoutChange('dagre')}>
                  Hierarchical (Dagre)
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleLayoutChange('cose')}>
                  Force-directed (Cose)
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleLayoutChange('grid')}>
                  Grid Layout
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleLayoutChange('circle')}>
                  Circular Layout
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Theme Toggle */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
                >
                  {theme === 'light' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                Switch to {theme === 'light' ? 'dark' : 'light'} theme
              </TooltipContent>
            </Tooltip>

            <Separator orientation="vertical" className="h-6" />

            {/* Share */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={handleShare}>
                  <Share2 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Copy to clipboard</TooltipContent>
            </Tooltip>

            {/* Export */}
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" onClick={handleExport}>
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Export flowchart</TooltipContent>
            </Tooltip>
          </div>

          {/* Tab Content */}
          <div className="flex-1 flex flex-col min-h-0">
            {/* Document Tab */}
            <Tabs.Content value="Document" className="flex-1 flex flex-col">
              <Allotment defaultSizes={[50, 50]} className="flex-1">
                {/* Left Panel - Editor */}
                <Allotment.Pane minSize={200}>
                  <div className="h-full flex flex-col">
                    {/* AI Toolbar */}
                    {!readOnly && (
                      <div className="border-b border-gray-200 dark:border-gray-700">
                        <AiToolbar
                          currentFlowchartText={text}
                          onFlowchartUpdate={setText}
                        />
                      </div>
                    )}

                    {/* Editor */}
                    <div className="flex-1">
                      <FlowchartTextEditor
                        readOnly={readOnly}
                        showToolbar={false}
                        showStatus={true}
                        className="h-full"
                      />
                    </div>
                  </div>
                </Allotment.Pane>

                {/* Right Panel - Graph */}
                <Allotment.Pane minSize={200}>
                  <div className="h-full flex flex-col">
                    {/* Graph Header */}
                    <div className="flex items-center justify-between p-3 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
                      <h2 className="text-sm font-medium text-gray-900 dark:text-white">Visual Preview</h2>
                      <div className="flex items-center gap-2">
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          {statusInfo.nodes} nodes • {statusInfo.edges} edges
                        </div>
                        <Select value={layoutAlgorithm} onValueChange={(value) => setLayoutAlgorithm(value as any)}>
                          <SelectTrigger className="w-24 h-7 text-xs">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="dagre">Dagre</SelectItem>
                            <SelectItem value="cose">COSE</SelectItem>
                            <SelectItem value="grid">Grid</SelectItem>
                            <SelectItem value="circle">Circle</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    {/* Graph Viewer */}
                    <div className="flex-1 relative">
                      <FlowchartGraphViewer
                        className="h-full"
                      />
                    </div>
                  </div>
                </Allotment.Pane>
              </Allotment>
            </Tabs.Content>

            {/* Theme Tab */}
            <Tabs.Content value="Theme" className="flex-1 p-6">
              <div className="max-w-md space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Theme Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="theme-toggle">Dark Mode</Label>
                      <Button
                        id="theme-toggle"
                        variant="outline"
                        size="sm"
                        onClick={toggleTheme}
                      >
                        {theme === 'dark' ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                        {theme === 'dark' ? 'Light' : 'Dark'}
                      </Button>
                    </div>

                    <div>
                      <Label htmlFor="layout-select">Layout Algorithm</Label>
                      <Select value={layoutAlgorithm} onValueChange={(value) => setLayoutAlgorithm(value as any)}>
                        <SelectTrigger className="w-full mt-2">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="dagre">Dagre (Hierarchical)</SelectItem>
                          <SelectItem value="cose">COSE (Force-directed)</SelectItem>
                          <SelectItem value="grid">Grid Layout</SelectItem>
                          <SelectItem value="circle">Circle Layout</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            {/* Examples Tab */}
            <Tabs.Content value="Examples" className="flex-1 p-6">
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Sample Flowcharts</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadExample('simple')}>
                      <CardHeader>
                        <CardTitle className="text-sm">Simple Process</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Basic flowchart with decision points</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadExample('login')}>
                      <CardHeader>
                        <CardTitle className="text-sm">User Login Flow</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Authentication process with error handling</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadExample('research')}>
                      <CardHeader>
                        <CardTitle className="text-sm">Research Process</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Academic research methodology workflow</p>
                      </CardContent>
                    </Card>

                    <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => loadExample('development')}>
                      <CardHeader>
                        <CardTitle className="text-sm">Software Development</CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-xs text-gray-600 dark:text-gray-400">Complete development lifecycle</p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            {/* Learn Syntax Tab */}
            <Tabs.Content value="Learn Syntax" className="flex-1 p-6">
              <div className="max-w-2xl space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Flowchart Syntax Guide</h3>
                  <div className="space-y-4">
                    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Basic Structure</h4>
                      <pre className="text-sm text-gray-700 dark:text-gray-300">
{`Start
  Process Data
  Decision: Valid?
    Yes: Save Data
    No: Show Error
  End`}
                      </pre>
                    </div>

                    <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
                      <h4 className="font-medium mb-2">Syntax Rules</h4>
                      <ul className="text-sm text-gray-700 dark:text-gray-300 space-y-1">
                        <li>• Each line represents a node/step</li>
                        <li>• Use 2-space indentation for hierarchy</li>
                        <li>• Use ":" for connection labels or descriptions</li>
                        <li>• Keep node names clear and concise</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            {/* Load File Tab */}
            <Tabs.Content value="Load File" className="flex-1 p-6">
              <div className="max-w-md space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Import File</h3>
                  <div className="space-y-4">
                    <Button onClick={handleFileImport} className="w-full">
                      <Upload className="h-4 w-4 mr-2" />
                      Choose File
                    </Button>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Supported formats: .txt, .md, .json
                    </p>
                  </div>
                </div>
              </div>
            </Tabs.Content>

            {/* Import Data Tab */}
            <Tabs.Content value="Import Data" className="flex-1 p-6">
              <div className="max-w-md space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Import Data</h3>
                  <div className="space-y-4">
                    <Textarea
                      placeholder="Paste your data here (CSV, JSON, or plain text)..."
                      className="min-h-32"
                    />
                    <Button className="w-full">
                      <Database className="h-4 w-4 mr-2" />
                      Convert to Flowchart
                    </Button>
                  </div>
                </div>
              </div>
            </Tabs.Content>
          </div>
        </Tabs.Root>

        {/* Export Dialog */}
        <FlowchartExportDialog
          isOpen={isExportDialogOpen}
          onClose={() => setIsExportDialogOpen(false)}
          onExport={() => setIsExportDialogOpen(false)}
          graph={parsedGraph}
          text={text}
        />
      </div>
    </TooltipProvider>
  );
}
