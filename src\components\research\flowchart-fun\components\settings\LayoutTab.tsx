/**
 * Layout Settings Tab
 * Configure layout algorithms and spacing
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { SettingsTabProps, LayoutName, Direction } from '../../types';

const layoutOptions: { value: LayoutName; label: string; description: string }[] = [
  { value: 'dagre', label: 'Dagre', description: 'Hierarchical layout with good node separation' },
  { value: 'klay', label: 'Klay', description: 'Advanced layered layout algorithm' },
  { value: 'breadthfirst', label: 'Breadth First', description: 'Tree-like structure from root' },
  { value: 'cose', label: 'CoSE', description: 'Force-directed layout with clustering' },
  { value: 'concentric', label: 'Concentric', description: 'Circular layout in concentric rings' },
  { value: 'circle', label: 'Circle', description: 'Simple circular arrangement' },
  { value: 'layered', label: 'Layered', description: 'Organized in distinct layers' },
  { value: 'mrtree', label: 'MR Tree', description: 'Multi-root tree layout' },
  { value: 'stress', label: 'Stress', description: 'Stress minimization layout' },
  { value: 'radial', label: 'Radial', description: 'Radial tree layout from center' },
];

const directionOptions: { value: Direction; label: string; description: string }[] = [
  { value: 'DOWN', label: 'Top to Bottom', description: 'Flows from top to bottom' },
  { value: 'UP', label: 'Bottom to Top', description: 'Flows from bottom to top' },
  { value: 'RIGHT', label: 'Left to Right', description: 'Flows from left to right' },
  { value: 'LEFT', label: 'Right to Left', description: 'Flows from right to left' },
];

const LayoutTab: React.FC<SettingsTabProps> = ({ theme, onThemeChange }) => {
  const handleLayoutChange = (layoutName: LayoutName) => {
    onThemeChange({ layoutName });
  };

  const handleDirectionChange = (direction: Direction) => {
    onThemeChange({ direction });
  };

  const handleSpacingChange = (value: number[]) => {
    onThemeChange({ spacingFactor: value[0] });
  };

  const selectedLayout = layoutOptions.find(opt => opt.value === theme.layoutName);
  const selectedDirection = directionOptions.find(opt => opt.value === theme.direction);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Layout Algorithm</CardTitle>
          <CardDescription>
            Choose how nodes are arranged in your flowchart
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="layout-select">Algorithm</Label>
            <Select value={theme.layoutName} onValueChange={handleLayoutChange}>
              <SelectTrigger id="layout-select">
                <SelectValue placeholder="Select layout algorithm" />
              </SelectTrigger>
              <SelectContent>
                {layoutOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedLayout && (
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary">{selectedLayout.label}</Badge>
                <span className="text-sm text-muted-foreground">
                  {selectedLayout.description}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Flow Direction</CardTitle>
          <CardDescription>
            Set the primary direction of your flowchart
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="direction-select">Direction</Label>
            <Select value={theme.direction} onValueChange={handleDirectionChange}>
              <SelectTrigger id="direction-select">
                <SelectValue placeholder="Select flow direction" />
              </SelectTrigger>
              <SelectContent>
                {directionOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex flex-col">
                      <span className="font-medium">{option.label}</span>
                      <span className="text-sm text-muted-foreground">{option.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedDirection && (
              <div className="flex items-center gap-2 mt-2">
                <Badge variant="secondary">{selectedDirection.label}</Badge>
                <span className="text-sm text-muted-foreground">
                  {selectedDirection.description}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Spacing</CardTitle>
          <CardDescription>
            Adjust the spacing between nodes and elements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="spacing-slider">Spacing Factor</Label>
              <Badge variant="outline">{theme.spacingFactor.toFixed(2)}</Badge>
            </div>
            <Slider
              id="spacing-slider"
              min={0.5}
              max={3.0}
              step={0.05}
              value={[theme.spacingFactor]}
              onValueChange={handleSpacingChange}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Compact (0.5)</span>
              <span>Normal (1.25)</span>
              <span>Spacious (3.0)</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LayoutTab;
