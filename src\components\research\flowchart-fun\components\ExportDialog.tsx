/**
 * Export Dialog Component
 * Dialog for exporting flowcharts in various formats
 */

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Download, 
  FileImage, 
  FileText, 
  File, 
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { ExportDialogProps, ExportFormat, ExportOptions } from '../types';
import { useFlowchartFunStore } from '../stores/flowchart-fun.store';
import { EXPORT_FORMATS, EXPORT_FORMAT_LABELS, DEFAULT_EXPORT_OPTIONS } from '../constants';

export function FlowchartExportDialog({ 
  isOpen, 
  onClose, 
  onExport,
  graph,
  text 
}: ExportDialogProps) {
  const [format, setFormat] = useState<ExportFormat>('png');
  const [filename, setFilename] = useState('flowchart');
  const [quality, setQuality] = useState(1.0);
  const [width, setWidth] = useState(1200);
  const [height, setHeight] = useState(800);
  const [backgroundColor, setBackgroundColor] = useState('#ffffff');
  const [includeMetadata, setIncludeMetadata] = useState(true);

  const {
    isExporting,
    exportProgress,
    lastExportResult,
    exportGraph
  } = useFlowchartFunStore();

  const handleExport = async () => {
    const options: ExportOptions = {
      format,
      filename: `${filename}.${format}`,
      quality: format === 'png' ? quality : undefined,
      width,
      height,
      backgroundColor,
      includeMetadata
    };

    const result = await exportGraph(options);
    
    if (result.success && result.data) {
      // Download the file
      if (result.data instanceof Blob) {
        const url = URL.createObjectURL(result.data);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.filename || options.filename || `flowchart.${format}`;
        a.click();
        URL.revokeObjectURL(url);
      } else {
        // Handle string data (JSON, text)
        const blob = new Blob([result.data], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = result.filename || options.filename || `flowchart.${format}`;
        a.click();
        URL.revokeObjectURL(url);
      }
    }

    onExport(options);
  };

  const getFormatIcon = (format: ExportFormat) => {
    switch (format) {
      case 'png':
      case 'svg':
      case 'pdf':
        return <FileImage className="h-4 w-4" />;
      case 'txt':
      case 'json':
        return <FileText className="h-4 w-4" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  const getFormatDescription = (format: ExportFormat) => {
    switch (format) {
      case 'png':
        return 'High-quality raster image suitable for presentations and documents';
      case 'svg':
        return 'Scalable vector graphics perfect for web and print';
      case 'pdf':
        return 'Portable document format for sharing and archiving';
      case 'word':
        return 'Microsoft Word document with embedded flowchart';
      case 'json':
        return 'Machine-readable data format for programmatic use';
      case 'txt':
        return 'Plain text format of the flowchart syntax';
      default:
        return 'Export in the selected format';
    }
  };

  const isImageFormat = ['png', 'svg', 'pdf'].includes(format);
  const isDataFormat = ['json', 'txt'].includes(format);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <span>Export Flowchart</span>
          </DialogTitle>
          <DialogDescription>
            Choose your export format and customize the output settings.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-2">
            <Label htmlFor="format">Export Format</Label>
            <Select value={format} onValueChange={(value) => setFormat(value as ExportFormat)}>
              <SelectTrigger>
                <SelectValue placeholder="Select format" />
              </SelectTrigger>
              <SelectContent>
                {EXPORT_FORMATS.map((fmt) => (
                  <SelectItem key={fmt} value={fmt}>
                    <div className="flex items-center space-x-2">
                      {getFormatIcon(fmt)}
                      <span>{EXPORT_FORMAT_LABELS[fmt]}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {getFormatDescription(format)}
            </p>
          </div>

          {/* Filename */}
          <div className="space-y-2">
            <Label htmlFor="filename">Filename</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="filename"
                value={filename}
                onChange={(e) => setFilename(e.target.value)}
                placeholder="Enter filename"
              />
              <span className="text-sm text-gray-500">.{format}</span>
            </div>
          </div>

          {/* Image-specific options */}
          {isImageFormat && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="width">Width (px)</Label>
                  <Input
                    id="width"
                    type="number"
                    value={width}
                    onChange={(e) => setWidth(parseInt(e.target.value) || 1200)}
                    min="100"
                    max="5000"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="height">Height (px)</Label>
                  <Input
                    id="height"
                    type="number"
                    value={height}
                    onChange={(e) => setHeight(parseInt(e.target.value) || 800)}
                    min="100"
                    max="5000"
                  />
                </div>
              </div>

              {format === 'png' && (
                <div className="space-y-2">
                  <Label htmlFor="quality">Quality</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="quality"
                      type="range"
                      min="0.1"
                      max="1"
                      step="0.1"
                      value={quality}
                      onChange={(e) => setQuality(parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <span className="text-sm text-gray-600 w-12">{Math.round(quality * 100)}%</span>
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="backgroundColor">Background Color</Label>
                <div className="flex items-center space-x-2">
                  <Input
                    id="backgroundColor"
                    type="color"
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    className="w-16 h-10"
                  />
                  <Input
                    value={backgroundColor}
                    onChange={(e) => setBackgroundColor(e.target.value)}
                    placeholder="#ffffff"
                    className="flex-1"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Data format options */}
          {(format === 'word' || format === 'json') && (
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeMetadata"
                  checked={includeMetadata}
                  onCheckedChange={(checked) => setIncludeMetadata(checked as boolean)}
                />
                <Label htmlFor="includeMetadata">Include metadata</Label>
              </div>
            </div>
          )}

          {/* Export Progress */}
          {isExporting && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Exporting...</span>
                <span className="text-sm text-gray-600">{exportProgress}%</span>
              </div>
              <Progress value={exportProgress} className="w-full" />
            </div>
          )}

          {/* Export Result */}
          {lastExportResult && !isExporting && (
            <Alert variant={lastExportResult.success ? "default" : "destructive"}>
              {lastExportResult.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {lastExportResult.success 
                  ? 'Export completed successfully!'
                  : `Export failed: ${lastExportResult.error}`
                }
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isExporting}>
            Cancel
          </Button>
          <Button 
            onClick={handleExport} 
            disabled={isExporting || !graph || !filename.trim()}
          >
            {isExporting ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Exporting...
              </>
            ) : (
              <>
                <Download className="h-4 w-4 mr-2" />
                Export
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
