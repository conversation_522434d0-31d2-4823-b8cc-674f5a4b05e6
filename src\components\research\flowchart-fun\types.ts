/**
 * Flowchart Fun Types
 * TypeScript interfaces for the Flowchart Fun module
 */

import { Core, ElementDefinition, NodeDefinition, EdgeDefinition } from 'cytoscape';

// Core flowchart types
export interface FlowchartNode {
  id: string;
  label: string;
  data?: Record<string, any>;
  position?: { x: number; y: number };
  classes?: string[];
  style?: Record<string, any>;
}

export interface FlowchartEdge {
  id: string;
  source: string;
  target: string;
  label?: string;
  data?: Record<string, any>;
  classes?: string[];
  style?: Record<string, any>;
}

export interface FlowchartGraph {
  nodes: FlowchartNode[];
  edges: FlowchartEdge[];
  metadata?: FlowchartMetadata;
}

export interface FlowchartMetadata {
  title?: string;
  description?: string;
  author?: string;
  createdAt: Date;
  updatedAt: Date;
  version?: string;
  tags?: string[];
}

// Text syntax types
export interface ParsedLine {
  content: string;
  indentLevel: number;
  lineNumber: number;
  nodeId?: string;
  connections?: string[];
  isComment?: boolean;
}

export interface ParseResult {
  success: boolean;
  graph?: FlowchartGraph;
  errors?: ParseError[];
  warnings?: string[];
}

export interface ParseError {
  line: number;
  column?: number;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

// Export types
export type ExportFormat = 'png' | 'svg' | 'pdf' | 'json' | 'cytoscape' | 'word' | 'txt';

export interface ExportOptions {
  format: ExportFormat;
  quality?: number;
  width?: number;
  height?: number;
  backgroundColor?: string;
  filename?: string;
  includeMetadata?: boolean;
}

export interface ExportResult {
  success: boolean;
  data?: Blob | string;
  filename?: string;
  error?: string;
}

// Component props
export interface FlowchartFunProps {
  className?: string;
  initialText?: string;
  onTextChange?: (text: string) => void;
  onGraphChange?: (graph: FlowchartGraph) => void;
  readOnly?: boolean;
}

export interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  readOnly?: boolean;
  className?: string;
  onCursorPositionChange?: (line: number, column: number) => void;
}

export interface GraphViewerProps {
  graph: FlowchartGraph;
  className?: string;
  onNodeClick?: (node: FlowchartNode) => void;
  onEdgeClick?: (edge: FlowchartEdge) => void;
  onBackgroundClick?: () => void;
  interactive?: boolean;
}

export interface ExportDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (options: ExportOptions) => void;
  graph: FlowchartGraph;
  text: string;
}

// Store types
export interface FlowchartFunState {
  // Text and parsing
  text: string;
  parsedGraph: FlowchartGraph | null;
  parseErrors: ParseError[];
  isValidSyntax: boolean;
  
  // UI state
  isLoading: boolean;
  selectedNode: string | null;
  selectedEdge: string | null;
  cursorPosition: { line: number; column: number };
  
  // Export state
  isExporting: boolean;
  exportProgress: number;
  lastExportResult: ExportResult | null;
  
  // Settings
  autoSave: boolean;
  theme: 'light' | 'dark';
  layoutAlgorithm: 'dagre' | 'cose' | 'grid' | 'circle';
  
  // History
  history: FlowchartHistory[];
  historyIndex: number;
  canUndo: boolean;
  canRedo: boolean;
}

export interface FlowchartFunActions {
  // Initialization
  initialize: () => void;

  // Text operations
  setText: (text: string) => void;
  updateText: (text: string) => void;
  insertText: (text: string, position?: { line: number; column: number }) => void;

  // Graph operations
  parseText: () => void;
  updateGraph: (graph: FlowchartGraph) => void;
  selectNode: (nodeId: string | null) => void;
  selectEdge: (edgeId: string | null) => void;
  
  // Export operations
  exportGraph: (options: ExportOptions) => Promise<ExportResult>;
  setExportProgress: (progress: number) => void;
  
  // History operations
  undo: () => void;
  redo: () => void;
  addToHistory: (entry: FlowchartHistory) => void;
  clearHistory: () => void;
  
  // Settings
  setTheme: (theme: 'light' | 'dark') => void;
  setLayoutAlgorithm: (algorithm: 'dagre' | 'cose' | 'grid' | 'circle') => void;
  setAutoSave: (enabled: boolean) => void;
  
  // UI operations
  setCursorPosition: (line: number, column: number) => void;
  setLoading: (loading: boolean) => void;
  clearSelection: () => void;
}

export interface FlowchartHistory {
  id: string;
  text: string;
  graph: FlowchartGraph;
  timestamp: Date;
  description?: string;
}

export type FlowchartFunStore = FlowchartFunState & FlowchartFunActions;

// Service types
export interface TextParserService {
  parse: (text: string) => ParseResult;
  validateSyntax: (text: string) => ParseError[];
  formatText: (text: string) => string;
  getNodeAtLine: (text: string, line: number) => FlowchartNode | null;
}

export interface GraphRenderService {
  render: (graph: FlowchartGraph, container: HTMLElement) => Core;
  updateGraph: (cy: Core, graph: FlowchartGraph) => void;
  applyLayout: (cy: Core, algorithm: string) => void;
  exportImage: (cy: Core, options: ExportOptions) => Promise<Blob>;
}

export interface ExportService {
  exportToPNG: (graph: FlowchartGraph, options: ExportOptions) => Promise<Blob>;
  exportToSVG: (graph: FlowchartGraph, options: ExportOptions) => Promise<Blob>;
  exportToPDF: (graph: FlowchartGraph, options: ExportOptions) => Promise<Blob>;
  exportToWord: (graph: FlowchartGraph, text: string, options: ExportOptions) => Promise<Blob>;
  exportToJSON: (graph: FlowchartGraph, options: ExportOptions) => string;
}

// Configuration types
export interface FlowchartConfig {
  defaultText: string;
  maxNodes: number;
  maxEdges: number;
  autoParseDelay: number;
  exportFormats: ExportFormat[];
  themes: Record<string, any>;
  layouts: Record<string, any>;
}

// Error types
export interface FlowchartError extends Error {
  code: string;
  line?: number;
  column?: number;
  severity: 'error' | 'warning' | 'info';
}

// Template types
export interface FlowchartTemplate {
  id: string;
  name: string;
  description: string;
  text: string;
  category: string;
  tags: string[];
  preview?: string;
}

// AI Integration types
export interface AIFlowchartRequest {
  prompt: string;
  context?: string;
  style?: 'simple' | 'detailed' | 'professional';
  maxNodes?: number;
}

export interface AIFlowchartResponse {
  success: boolean;
  text?: string;
  graph?: FlowchartGraph;
  explanation?: string;
  error?: string;
}

// Advanced Theme System Types (based on reference implementation)
export type LayoutName =
  | "dagre"
  | "klay"
  | "breadthfirst"
  | "cose"
  | "concentric"
  | "circle"
  | "layered"
  | "mrtree"
  | "stress"
  | "radial";

export type LayoutDirection = "TB" | "LR" | "RL" | "BT";

export type Direction = "RIGHT" | "LEFT" | "DOWN" | "UP";

export type Shape = "rectangle" | "roundrectangle" | "ellipse";

export type CurveStyle = "bezier" | "taxi" | "round-taxi";

export type Border = "none" | "solid" | "dashed" | "dotted" | "double";

export type ArrowShape =
  | "none"
  | "triangle"
  | "vee"
  | "triangle-backcurve"
  | "circle";

export interface FFTheme {
  // Global Style
  fontFamily: string;
  background: string;
  lineHeight: number;

  // Layout
  layoutName: LayoutName;
  direction: Direction;
  spacingFactor: number;

  // Node
  shape: Shape;
  nodeBackground: string;
  nodeForeground: string;
  padding: number;
  borderWidth: number;
  borderColor: string;
  textMaxWidth: number;
  textMarginY: number;
  useFixedHeight: boolean;
  fixedHeight: number;

  // Edge
  curveStyle: CurveStyle;
  edgeWidth: number;
  edgeColor: string;
  sourceArrowShape: ArrowShape;
  targetArrowShape: ArrowShape;
  sourceDistanceFromNode: number;
  targetDistanceFromNode: number;
  arrowScale: number;
  edgeTextSize: number;
  rotateEdgeLabel: boolean;
}

// Settings Panel Types
export interface SettingsTabProps {
  theme: FFTheme;
  onThemeChange: (theme: Partial<FFTheme>) => void;
}

export interface ThemePreset {
  id: string;
  name: string;
  description: string;
  theme: FFTheme;
  preview?: string;
}

// Layout Configuration
export interface LayoutConfig {
  name: LayoutName;
  options: Record<string, any>;
  description: string;
  supportedDirections: Direction[];
}

// Export Enhancement Types
export interface AdvancedExportOptions extends ExportOptions {
  theme?: FFTheme;
  layout?: LayoutConfig;
  customCSS?: string;
  watermark?: boolean;
  metadata?: FlowchartMetadata;
}
